import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/chat_models.dart';
import '../services/chat_service.dart';
import '../services/auth_service.dart';
import '../themes/app_theme.dart';
import 'item_detail_screen.dart';

class ChatScreen extends StatefulWidget {
  final ChatRoom chatRoom;

  const ChatScreen({
    super.key,
    required this.chatRoom,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  ChatParticipant? _otherParticipant;
  Map<String, dynamic>? _targetItem;
  Map<String, dynamic>? _offeredItem;
  bool _isLoading = true;
  String? _swapRequestStatus;
  bool _isProcessingRequest = false;

  @override
  void initState() {
    super.initState();
    _loadChatData();
    _markMessagesAsRead();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadChatData() async {
    try {
      final currentUser = AuthService.currentUser;
      if (currentUser == null) return;

      // Get other participant data
      final otherParticipantId = widget.chatRoom.participants
          .firstWhere((id) => id != currentUser.uid);
      _otherParticipant = await ChatService.getUserData(otherParticipantId);

      // Get item data
      if (widget.chatRoom.targetItemId != null) {
        _targetItem = await ChatService.getItemData(widget.chatRoom.targetItemId!);
      }
      if (widget.chatRoom.offeredItemId != null) {
        _offeredItem = await ChatService.getItemData(widget.chatRoom.offeredItemId!);
      }

      // Get swap request status
      if (widget.chatRoom.swapRequestId != null) {
        _swapRequestStatus = await _getSwapRequestStatus(widget.chatRoom.swapRequestId!);
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading chat data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _markMessagesAsRead() async {
    await ChatService.markMessagesAsRead(widget.chatRoom.chatId);
  }

  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    final currentUser = AuthService.currentUser;
    if (currentUser == null) return;

    _messageController.clear();

    final success = await ChatService.sendMessage(
      chatId: widget.chatRoom.chatId,
      senderId: currentUser.uid,
      message: message,
      type: MessageType.text,
    );

    if (success) {
      _scrollToBottom();
    } else {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to send message'),
            backgroundColor: AppTheme.getErrorColor(context),
          ),
        );
      }
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingView() : _buildChatView(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppTheme.getBackgroundColor(context),
      elevation: 0,
      titleSpacing: 1,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: Icon(
          Icons.arrow_back,
          color: AppTheme.getTextColor(context),
        ),
      ),
      title: Row(
        children: [
          // Profile picture
          _otherParticipant == null
              ? Shimmer.fromColors(
                  baseColor: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                  highlightColor: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.1),
                  child: CircleAvatar(
                    radius: 20,
                    backgroundColor: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                  ),
                )
              : CircleAvatar(
                  radius: 20,
                  backgroundColor: AppTheme.getPrimaryColor(context),
                  backgroundImage: _otherParticipant?.profileImageUrl != null
                      ? CachedNetworkImageProvider(_otherParticipant!.profileImageUrl!)
                      : null,
                  child: _otherParticipant?.profileImageUrl == null
                      ? Text(
                          _otherParticipant?.name.isNotEmpty == true
                              ? _otherParticipant!.name[0].toUpperCase()
                              : '?',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      : null,
                ),
          const SizedBox(width: 12),

          // Name and status
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _otherParticipant == null
                    ? Shimmer.fromColors(
                        baseColor: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                        highlightColor: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.1),
                        child: Container(
                          height: 16,
                          width: 120,
                          decoration: BoxDecoration(
                            color: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      )
                    : Text(
                        _otherParticipant!.name,
                        style: TextStyle(
                          color: AppTheme.getTextColor(context),
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                const SizedBox(height: 4),
                Text(
                  widget.chatRoom.requestType == 'swap' ? 'Swap Request' : 'Buy Request',
                  style: TextStyle(
                    color: AppTheme.getSecondaryTextColor(context),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildChatView() {
    return Column(
      children: [
        // Item info header
        if (_targetItem != null || _offeredItem != null) _buildItemHeader(),

        // Messages
        Expanded(
          child: StreamBuilder<List<ChatMessage>>(
            stream: ChatService.getChatMessages(widget.chatRoom.chatId),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              if (snapshot.hasError) {
                return Center(
                  child: Text(
                    'Error loading messages',
                    style: TextStyle(
                      color: AppTheme.getErrorColor(context),
                      fontSize: 16,
                    ),
                  ),
                );
              }

              final messages = snapshot.data ?? [];
              if (messages.isEmpty) {
                return Center(
                  child: Text(
                    'Start the conversation!',
                    style: TextStyle(
                      color: AppTheme.getSecondaryTextColor(context),
                      fontSize: 16,
                    ),
                  ),
                );
              }

              WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToBottom());

              return ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(16),
                itemCount: messages.length,
                itemBuilder: (context, index) {
                  return _buildMessageBubble(messages[index]);
                },
              );
            },
          ),
        ),

        // Message input
        _buildMessageInput(),
      ],
    );
  }

  Widget _buildItemHeader() {
    final currentUser = AuthService.currentUser;
    if (currentUser == null) return const SizedBox.shrink();

    // Determine which item belongs to current user and which to other user
    Map<String, dynamic>? myItem;
    Map<String, dynamic>? otherItem;

    // Check if current user is the owner of target item or requester
    final isOwner = _targetItem?['userId'] == currentUser.uid;

    if (isOwner) {
      // Current user is owner, so target item is theirs, offered item is other's
      myItem = _targetItem;
      otherItem = _offeredItem;
    } else {
      // Current user is requester, so offered item is theirs, target item is other's
      myItem = _offeredItem;
      otherItem = _targetItem;
    }

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          // Items row
          Row(
            children: [
              // My item (left side)
              Expanded(
                child: _buildItemCard(
                  item: myItem,
                  label: 'Your Item',
                  isClickable: false,
                ),
              ),

              // Arrows in center
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    Icon(
                      Icons.arrow_forward,
                      color: AppTheme.getPrimaryColor(context),
                      size: 20,
                    ),
                    const SizedBox(height: 4),
                    Icon(
                      Icons.arrow_back,
                      color: AppTheme.getPrimaryColor(context),
                      size: 20,
                    ),
                  ],
                ),
              ),

              // Other user's item (right side)
              Expanded(
                child: _buildItemCard(
                  item: otherItem,
                  label: 'Their Item',
                  isClickable: true,
                ),
              ),
            ],
          ),

          // Accept/Reject buttons (only for owner and if request is pending)
          if (_shouldShowActionButtons()) ...[
            const SizedBox(height: 16),
            _buildActionButtons(),
          ],
        ],
      ),
    );
  }

  Widget _buildItemCard({
    required Map<String, dynamic>? item,
    required String label,
    required bool isClickable,
  }) {
    if (item == null) {
      return Container(
        height: 100,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: AppTheme.getBackgroundColor(context),
          border: Border.all(
            color: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
            style: BorderStyle.solid,
          ),
        ),
        child: Center(
          child: Text(
            widget.chatRoom.requestType == 'swap' ? 'No Item' : 'Cash Offer',
            style: TextStyle(
              color: AppTheme.getSecondaryTextColor(context),
              fontSize: 12,
            ),
          ),
        ),
      );
    }

    final images = List<String>.from(item['images'] ?? []);
    final imageUrl = images.isNotEmpty ? images[0] : null;

    Widget itemWidget = Container(
      height: 100,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: AppTheme.getBackgroundColor(context),
        border: Border.all(
          color: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.2),
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: imageUrl != null
            ? CachedNetworkImage(
                imageUrl: imageUrl,
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                placeholder: (context, url) => Container(
                  color: AppTheme.getBackgroundColor(context),
                  child: Icon(
                    Icons.image,
                    color: AppTheme.getSecondaryTextColor(context),
                    size: 24,
                  ),
                ),
                errorWidget: (context, url, error) => Icon(
                  Icons.image_not_supported,
                  color: AppTheme.getSecondaryTextColor(context),
                  size: 24,
                ),
              )
            : Icon(
                Icons.image_not_supported,
                color: AppTheme.getSecondaryTextColor(context),
                size: 24,
              ),
      ),
    );

    if (isClickable) {
      return GestureDetector(
        onTap: () => _navigateToItemDetail(item),
        child: itemWidget,
      );
    }

    return itemWidget;
  }

  void _navigateToItemDetail(Map<String, dynamic> item) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ItemDetailScreen(
          itemId: item['docId'] ?? item['itemId'] ?? '',
          itemData: item,
        ),
      ),
    );
  }

  bool _shouldShowActionButtons() {
    final currentUser = AuthService.currentUser;
    if (currentUser == null || _targetItem == null) return false;

    // Only show to the owner of the target item
    final isOwner = _targetItem!['userId'] == currentUser.uid;

    // Only show if request is pending (not accepted or rejected)
    final isPending = _swapRequestStatus == null || _swapRequestStatus == 'pending';

    return isOwner && isPending;
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        // Reject button
        Expanded(
          child: Container(
            height: 44,
            margin: const EdgeInsets.only(right: 8),
            child: ElevatedButton(
              onPressed: _isProcessingRequest
                  ? null
                  : () => _updateSwapRequestStatus('rejected'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade400,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(22),
                ),
                elevation: 0,
              ),
              child: _isProcessingRequest
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.close, size: 18),
                        const SizedBox(width: 4),
                        Text('Reject', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
                      ],
                    ),
            ),
          ),
        ),

        // Accept button
        Expanded(
          child: Container(
            height: 44,
            margin: const EdgeInsets.only(left: 8),
            child: ElevatedButton(
              onPressed: _isProcessingRequest
                  ? null
                  : () => _updateSwapRequestStatus('accepted'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green.shade400,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(22),
                ),
                elevation: 0,
              ),
              child: _isProcessingRequest
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.check, size: 18),
                        const SizedBox(width: 4),
                        Text('Accept', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
                      ],
                    ),
            ),
          ),
        ),
      ],
    );
  }

  Future<String?> _getSwapRequestStatus(String swapRequestId) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('swapRequests')
          .doc(swapRequestId)
          .get();

      if (doc.exists) {
        return doc.data()?['status'] as String?;
      }
      return null;
    } catch (e) {
      print('Error getting swap request status: $e');
      return null;
    }
  }

  Future<void> _updateSwapRequestStatus(String status) async {
    if (widget.chatRoom.swapRequestId == null || _isProcessingRequest) return;

    setState(() {
      _isProcessingRequest = true;
    });

    try {
      // Update swap request status in Firestore
      await FirebaseFirestore.instance
          .collection('swapRequests')
          .doc(widget.chatRoom.swapRequestId!)
          .update({'status': status});

      // Send system message to inform both users
      final currentUser = AuthService.currentUser;
      if (currentUser != null) {
        String systemMessage;
        if (status == 'accepted') {
          systemMessage = 'Swap request has been accepted! 🎉';
        } else {
          systemMessage = 'Swap request has been rejected.';
        }

        await ChatService.sendMessage(
          chatId: widget.chatRoom.chatId,
          senderId: 'system',
          message: systemMessage,
          type: MessageType.system,
        );
      }

      // Update local state
      if (mounted) {
        setState(() {
          _swapRequestStatus = status;
          _isProcessingRequest = false;
        });
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              status == 'accepted'
                  ? 'Swap request accepted!'
                  : 'Swap request rejected',
            ),
            backgroundColor: status == 'accepted'
                ? Colors.green
                : Colors.orange,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      }
    } catch (e) {
      print('Error updating swap request status: $e');
      if (mounted) {
        setState(() {
          _isProcessingRequest = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to update request status'),
            backgroundColor: AppTheme.getErrorColor(context),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      }
    }
  }

  Widget _buildMessageBubble(ChatMessage message) {
    final currentUser = AuthService.currentUser;
    final isMe = message.senderId == currentUser?.uid;
    final isSystem = message.senderId == 'system';

    if (isSystem) {
      return _buildSystemMessage(message);
    }

    return Align(
      alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        decoration: BoxDecoration(
          color: isMe
              ? AppTheme.getPrimaryColor(context).withValues(alpha: 0.8)
              : AppTheme.getSurfaceColor(context),
          borderRadius: BorderRadius.circular(18),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message.message,
              style: TextStyle(
                color: isMe ? Colors.white : AppTheme.getTextColor(context),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _formatTime(message.timestamp),
              style: TextStyle(
                color: isMe
                    ? Colors.white.withValues(alpha: 0.7)
                    : AppTheme.getSecondaryTextColor(context),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemMessage(ChatMessage message) {
    final currentUser = AuthService.currentUser;
    if (currentUser == null) return const SizedBox.shrink();

    // Don't show the initial swap request system message
    if (message.type == MessageType.swapRequest &&
        (message.message.contains('wants to swap') || message.message.contains('wants to buy'))) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: AppTheme.getPrimaryColor(context),
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message.message,
              style: TextStyle(
                color: AppTheme.getTextColor(context),
                fontSize: 14,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'Type a message...',
                hintStyle: TextStyle(
                  color: AppTheme.getSecondaryTextColor(context),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(
                    color: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(
                    color: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide(
                    color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.6),
                    width: 1.5,
                  ),
                ),
                filled: true,
                fillColor: AppTheme.getBackgroundColor(context),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              style: TextStyle(
                color: AppTheme.getTextColor(context),
              ),
              maxLines: null,
              textCapitalization: TextCapitalization.sentences,
            ),
          ),
          const SizedBox(width: 8),

          // Send button
          Container(
            decoration: BoxDecoration(
              color: AppTheme.getPrimaryColor(context),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: _sendMessage,
              icon: const Icon(
                Icons.send,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${timestamp.day}/${timestamp.month}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
