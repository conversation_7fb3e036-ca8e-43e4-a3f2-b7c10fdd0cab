import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../themes/app_theme.dart';
import '../services/auth_service.dart';
import '../services/swap_service.dart';
import '../widgets/swap_modals.dart';

class ItemDetailScreen extends StatefulWidget {
  final String itemId;
  final Map<String, dynamic> itemData;
  final String? heroTag;

  const ItemDetailScreen({
    super.key,
    required this.itemId,
    required this.itemData,
    this.heroTag,
  });

  @override
  State<ItemDetailScreen> createState() => _ItemDetailScreenState();
}

class _ItemDetailScreenState extends State<ItemDetailScreen> {
  final PageController _pageController = PageController();
  int _currentImageIndex = 0;
  Map<String, dynamic>? _ownerData;
  bool _isLoadingOwner = true;
  bool _isLiked = false;
  bool _isLikeLoading = false;
  Map<String, dynamic>? _ownerVerificationData;

  @override
  void initState() {
    super.initState();
    _loadOwnerData();
    _loadLikeStatus();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadOwnerData() async {
    try {
      final userId = widget.itemData['userId'];
      if (userId != null) {
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(userId)
            .get();

        if (userDoc.exists && mounted) {
          final userData = userDoc.data();
          final verificationData = await _getUserVerificationData(userId);

          setState(() {
            _ownerData = userData;
            _ownerVerificationData = verificationData;
            _isLoadingOwner = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingOwner = false;
        });
      }
    }
  }

  Future<Map<String, dynamic>> _getUserVerificationData(String userId) async {
    try {
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .get();

      if (userDoc.exists) {
        final data = userDoc.data()!;
        final emailVerified = data['emailVerified'] == true;
        final phoneVerified = data['phoneVerified'] == true;
        final documentVerified = data['documentVerified'] == true;

        int verificationCount = 0;
        if (emailVerified) verificationCount++;
        if (phoneVerified) verificationCount++;
        if (documentVerified) verificationCount++;

        return {
          'emailVerified': emailVerified,
          'phoneVerified': phoneVerified,
          'documentVerified': documentVerified,
          'verificationCount': verificationCount,
        };
      }
    } catch (e) {
      debugPrint('Error fetching verification data: $e');
    }

    return {
      'emailVerified': false,
      'phoneVerified': false,
      'documentVerified': false,
      'verificationCount': 0,
    };
  }

  Future<void> _loadLikeStatus() async {
    try {
      final currentUser = AuthService.currentUser;
      if (currentUser != null) {
        final likeDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(currentUser.uid)
            .collection('likedItems')
            .doc(widget.itemId)
            .get();

        if (mounted) {
          setState(() {
            _isLiked = likeDoc.exists;
          });
        }
      }
    } catch (e) {
      debugPrint('Error loading like status: $e');
    }
  }

  Future<void> _toggleLike() async {
    final currentUser = AuthService.currentUser;
    if (currentUser == null) return;

    setState(() => _isLikeLoading = true);

    try {
      final likeRef = FirebaseFirestore.instance
          .collection('users')
          .doc(currentUser.uid)
          .collection('likedItems')
          .doc(widget.itemId);

      if (_isLiked) {
        // Remove like
        await likeRef.delete();
        setState(() => _isLiked = false);
      } else {
        // Add like
        await likeRef.set({
          'itemId': widget.itemId,
          'likedAt': FieldValue.serverTimestamp(),
          'itemData': widget.itemData,
        });
        setState(() => _isLiked = true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update like: ${e.toString()}'),
            backgroundColor: AppTheme.getErrorColor(context),
          ),
        );
      }
    } finally {
      setState(() => _isLikeLoading = false);
    }
  }

  Widget _buildVerificationBadge(int verificationCount) {
    if (verificationCount == 0) {
      return const SizedBox.shrink(); // No badge for unverified users
    } else if (verificationCount == 1) {
      // 1 verification - yellow badge
      return Container(
        margin: const EdgeInsets.only(left: 6),
        child: Icon(
          Icons.verified,
          color: Colors.amber,
          size: 18,
        ),
      );
    } else if (verificationCount == 2) {
      // 2 verifications - orange badge
      return Container(
        margin: const EdgeInsets.only(left: 6),
        child: Icon(
          Icons.verified,
          color: Colors.orange,
          size: 18,
        ),
      );
    } else {
      // 3 verifications - green badge
      return Container(
        margin: const EdgeInsets.only(left: 6),
        child: Icon(
          Icons.verified,
          color: Colors.green,
          size: 18,
        ),
      );
    }
  }

  String _formatDistance(double? distance) {
    if (distance == null) return '';

    if (distance < 1) {
      return '${(distance * 1000).round()}m away';
    } else {
      return '${distance.toStringAsFixed(1)}km away';
    }
  }

  void _showReportModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _ReportModal(
        itemId: widget.itemId,
        itemData: widget.itemData,
      ),
    );
  }

  void _onMessageTap() {
    // TODO: Navigate to message screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Message functionality coming soon!'),
        backgroundColor: AppTheme.getPrimaryColor(context),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _onOfferToSwapTap() async {
    try {
      // Check if user has posted any items
      final hasPostedItems = await SwapService.hasUserPostedItems();

      if (!mounted) return;

      final targetItemId = widget.itemData['itemId'] ?? widget.itemId;
      final targetOwnerId = widget.itemData['userId'];

      if (targetOwnerId == null) {
        _showErrorSnackBar('Unable to process request. Item owner not found.');
        return;
      }

      // Success callback for when request is sent
      void onSuccess() {
        // You can add any additional logic here if needed
        // For now, we just show the success message in the modal
      }

      if (!hasPostedItems) {
        // Show modal for users with no posted items
        await SwapModals.showNoPreviousItemsModal(
          context: context,
          targetItemId: targetItemId,
          targetOwnerId: targetOwnerId,
          onSuccess: onSuccess,
        );
      } else {
        // Get user's posted items and show selection modal
        final userItems = await SwapService.getUserPostedItems();

        if (!mounted) return;

        if (userItems.isEmpty) {
          // Fallback to no items modal if fetch failed
          await SwapModals.showNoPreviousItemsModal(
            context: context,
            targetItemId: targetItemId,
            targetOwnerId: targetOwnerId,
            onSuccess: onSuccess,
          );
        } else {
          // Show item selection modal
          await SwapModals.showItemSelectionModal(
            context: context,
            userItems: userItems,
            targetItemId: targetItemId,
            targetOwnerId: targetOwnerId,
            onSuccess: onSuccess,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to process swap request: ${e.toString()}');
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.getErrorColor(context),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  String _getSwapOptionText(String? swapOption) {
    switch (swapOption) {
      case 'Permanent Swap':
        return 'Permanent';
      case 'Temporary Swap':
        return 'Temporary';
      default:
        return 'Unknown';
    }
  }

  String _getConditionText(String? condition) {
    switch (condition) {
      case 'Brand New':
        return 'Brand New';
      case 'Like New':
        return 'Like New';
      case 'Good':
        return 'Good';
      case 'Fair':
        return 'Fair';
      case 'Poor':
        return 'Poor';
      default:
        return 'Unknown';
    }
  }

  @override
  Widget build(BuildContext context) {
    final images = List<String>.from(widget.itemData['images'] ?? []);
    final price = widget.itemData['price'];
    final isPriceNegotiable = widget.itemData['isPriceNegotiable'] ?? false;

    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        backgroundColor: AppTheme.getBackgroundColor(context),
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back,
            color: AppTheme.getTextColor(context),
          ),
        ),
        // No title as requested
      ),
      body: Column(
        children: [
          // Main content
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Image carousel with dot indicators
                  if (images.isNotEmpty) _buildImageCarousel(images),

                  // Content
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title with heart button
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                widget.itemData['itemName'] ?? 'Untitled Item',
                                style: TextStyle(
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.getTextColor(context),
                                ),
                              ),
                            ),
                            // Heart/Like button
                            GestureDetector(
                              onTap: _isLikeLoading ? null : _toggleLike,
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                child: _isLikeLoading
                                    ? SizedBox(
                                        width: 24,
                                        height: 24,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(
                                            AppTheme.getPrimaryColor(context),
                                          ),
                                        ),
                                      )
                                    : Icon(
                                        _isLiked ? Icons.favorite : Icons.favorite_border,
                                        color: _isLiked ? Colors.red : AppTheme.getSecondaryTextColor(context),
                                        size: 28,
                                      ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 12),

                        // Price
                        Row(
                          children: [
                            Text(
                              price != null ? '₹${price.toStringAsFixed(0)}' : 'Free',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.getPrimaryColor(context),
                              ),
                            ),
                            if (isPriceNegotiable) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.3),
                                  ),
                                ),
                                child: Text(
                                  'Negotiable',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.getPrimaryColor(context),
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Features pills (Category, Condition, Swap Option, Distance)
                        _buildFeaturePills(),

                        const SizedBox(height: 16),

                        // Description
                        _buildDescriptionSection(),

                        const SizedBox(height: 16),

                        // Owner details
                        _buildOwnerSection(),

                        const SizedBox(height: 8),

                        // Report this post
                        GestureDetector(
                          onTap: _showReportModal,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: Center(
                              child: Text(
                                'Report this post',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: AppTheme.getSecondaryTextColor(context),
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Bottom buttons
          _buildBottomButtons(),
        ],
      ),
    );
  }

  Widget _buildImageCarousel(List<String> images) {
    return SizedBox(
      height: 300,
      child: Stack(
        children: [
          // Image PageView
          PageView.builder(
            controller: _pageController,
            itemCount: images.length,
            onPageChanged: (index) {
              setState(() {
                _currentImageIndex = index;
              });
            },
            itemBuilder: (context, index) {
              final imageWidget = Container(
                width: double.infinity,
                height: 300,
                decoration: BoxDecoration(
                  color: AppTheme.getSurfaceColor(context),
                ),
                child: Image.network(
                  images[index],
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Center(
                      child: Icon(
                        Icons.image_not_supported,
                        size: 64,
                        color: AppTheme.getSecondaryTextColor(context),
                      ),
                    );
                  },
                ),
              );

              return GestureDetector(
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => _FullScreenImageViewer(
                        images: images,
                        initialIndex: index,
                      ),
                    ),
                  );
                },
                child: index == 0 && widget.heroTag != null
                    ? Hero(
                        tag: widget.heroTag!,
                        child: imageWidget,
                      )
                    : imageWidget,
              );
            },
          ),

          // Dot indicators
          if (images.length > 1)
            Positioned(
              bottom: 16,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  images.length,
                  (index) => AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: _currentImageIndex == index ? 24 : 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: _currentImageIndex == index
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(4),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFeaturePills() {
    final distance = widget.itemData['distance'] as double?;

    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: [
        // Category pill
        _buildFeaturePill(
          widget.itemData['category'] ?? 'Other',
          Icons.category,
          AppTheme.getPrimaryColor(context),
        ),

        // Condition pill
        _buildFeaturePill(
          _getConditionText(widget.itemData['condition']),
          Icons.star,
          AppTheme.getWarningColor(context),
        ),

        // Swap option pill
        _buildFeaturePill(
          _getSwapOptionText(widget.itemData['swapOption']),
          Icons.swap_horiz,
          AppTheme.getSuccessColor(context),
        ),

        // Distance pill
        if (distance != null)
          _buildFeaturePill(
            _formatDistance(distance),
            Icons.location_on,
            AppTheme.getSecondaryTextColor(context),
          ),
      ],
    );
  }

  Widget _buildFeaturePill(String text, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 6),
          Text(
            text,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionSection() {
    final description = widget.itemData['description'] ?? '';

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.getBorderColor(context),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Description',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.getTextColor(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            description.isNotEmpty ? description : 'No description provided.',
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.getSecondaryTextColor(context),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOwnerSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.getBorderColor(context),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Owner Details',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.getTextColor(context),
            ),
          ),
          const SizedBox(height: 12),

          if (_isLoadingOwner)
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 16,
                        width: 120,
                        decoration: BoxDecoration(
                          color: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        height: 14,
                        width: 80,
                        decoration: BoxDecoration(
                          color: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            )
          else
            Row(
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundColor: AppTheme.getPrimaryColor(context),
                  backgroundImage: _ownerData?['profileImageUrl'] != null
                      ? NetworkImage(_ownerData!['profileImageUrl'])
                      : null,
                  child: _ownerData?['profileImageUrl'] == null
                      ? Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 24,
                        )
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            _ownerData?['displayName'] ?? 'Unknown User',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.getTextColor(context),
                            ),
                          ),
                          // Verification badge
                          if (_ownerVerificationData != null)
                            _buildVerificationBadge(
                              _ownerVerificationData!['verificationCount'] as int,
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.itemData['city'] ?? 'Unknown Location',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.getSecondaryTextColor(context),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        border: Border(
          top: BorderSide(
            color: AppTheme.getBorderColor(context),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Message button (smaller, icon only)
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: AppTheme.getSecondaryTextColor(context),
                borderRadius: BorderRadius.circular(16),
              ),
              child: IconButton(
                onPressed: _onMessageTap,
                icon: const Icon(
                  Icons.message,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),

            const SizedBox(width: 16),

            // Offer to Swap button (larger, full text)
            Expanded(
              child: ElevatedButton(
                onPressed: _onOfferToSwapTap,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.getPrimaryColor(context),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 2,
                ),
                child: Text(
                  'Offer To Swap',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Full screen image viewer
class _FullScreenImageViewer extends StatefulWidget {
  final List<String> images;
  final int initialIndex;

  const _FullScreenImageViewer({
    required this.images,
    required this.initialIndex,
  });

  @override
  State<_FullScreenImageViewer> createState() => _FullScreenImageViewerState();
}

class _FullScreenImageViewerState extends State<_FullScreenImageViewer> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
        ),
        title: Text(
          '${_currentIndex + 1} of ${widget.images.length}',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
        ),
      ),
      body: PageView.builder(
        controller: _pageController,
        itemCount: widget.images.length,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        itemBuilder: (context, index) {
          return Center(
            child: InteractiveViewer(
              panEnabled: true,
              boundaryMargin: const EdgeInsets.all(20),
              minScale: 0.5,
              maxScale: 4.0,
              child: Image.network(
                widget.images[index],
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      size: 64,
                      color: Colors.white54,
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }
}

// Report Modal Widget
class _ReportModal extends StatefulWidget {
  final String itemId;
  final Map<String, dynamic> itemData;

  const _ReportModal({
    required this.itemId,
    required this.itemData,
  });

  @override
  State<_ReportModal> createState() => _ReportModalState();
}

class _ReportModalState extends State<_ReportModal> {
  String? _selectedReason;
  final TextEditingController _detailsController = TextEditingController();
  bool _isSubmitting = false;

  final List<Map<String, dynamic>> _reportReasons = [
    {
      'title': 'Inappropriate Content',
      'subtitle': 'Contains offensive or inappropriate material',
      'icon': Icons.warning,
    },
    {
      'title': 'Spam or Fake Listing',
      'subtitle': 'This appears to be spam or a fake item',
      'icon': Icons.block,
    },
    {
      'title': 'Misleading Information',
      'subtitle': 'Item description or images are misleading',
      'icon': Icons.error_outline,
    },
    {
      'title': 'Prohibited Item',
      'subtitle': 'Item violates community guidelines',
      'icon': Icons.gavel,
    },
    {
      'title': 'Duplicate Listing',
      'subtitle': 'This item has been posted multiple times',
      'icon': Icons.copy,
    },
    {
      'title': 'Other',
      'subtitle': 'Something else that violates our policies',
      'icon': Icons.more_horiz,
    },
  ];

  @override
  void dispose() {
    _detailsController.dispose();
    super.dispose();
  }

  Future<void> _submitReport() async {
    if (_selectedReason == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Please select a reason for reporting'),
          backgroundColor: AppTheme.getErrorColor(context),
        ),
      );
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      final currentUser = AuthService.currentUser;
      if (currentUser != null) {
        await FirebaseFirestore.instance.collection('reports').add({
          'itemId': widget.itemId,
          'reportedBy': currentUser.uid,
          'reason': _selectedReason,
          'details': _detailsController.text.trim(),
          'itemData': widget.itemData,
          'reportedAt': FieldValue.serverTimestamp(),
          'status': 'pending',
        });

        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Report submitted successfully. Thank you for helping keep our community safe.'),
              backgroundColor: AppTheme.getSuccessColor(context),
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit report: ${e.toString()}'),
            backgroundColor: AppTheme.getErrorColor(context),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Icon(
                  Icons.flag,
                  color: AppTheme.getErrorColor(context),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Report this post',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.getTextColor(context),
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(
                    Icons.close,
                    color: AppTheme.getSecondaryTextColor(context),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Why are you reporting this post?',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.getTextColor(context),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Report reasons
                  ...(_reportReasons.map((reason) => _buildReasonTile(reason))),

                  const SizedBox(height: 20),

                  // Additional details
                  Text(
                    'Additional details (optional)',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.getTextColor(context),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _detailsController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      hintText: 'Provide more details about the issue...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: AppTheme.getBorderColor(context),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: AppTheme.getPrimaryColor(context),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),

          // Submit button
          Padding(
            padding: const EdgeInsets.all(20),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isSubmitting ? null : _submitReport,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.getErrorColor(context),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isSubmitting
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Submit Report',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReasonTile(Map<String, dynamic> reason) {
    final isSelected = _selectedReason == reason['title'];

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedReason = reason['title'];
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.getErrorColor(context).withValues(alpha: 0.1)
              : AppTheme.getSurfaceColor(context),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? AppTheme.getErrorColor(context)
                : AppTheme.getBorderColor(context),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              reason['icon'],
              color: isSelected
                  ? AppTheme.getErrorColor(context)
                  : AppTheme.getSecondaryTextColor(context),
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    reason['title'],
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isSelected
                          ? AppTheme.getErrorColor(context)
                          : AppTheme.getTextColor(context),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    reason['subtitle'],
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.getSecondaryTextColor(context),
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppTheme.getErrorColor(context),
                size: 20,
              ),
          ],
        ),
      ),
    );
  }
}