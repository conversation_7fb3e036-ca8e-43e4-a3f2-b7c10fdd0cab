import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import '../themes/app_theme.dart';
import '../models/chat_models.dart';
import '../services/chat_service.dart';
import '../services/auth_service.dart';
import 'chat_screen.dart';

class MessagesScreen extends StatefulWidget {
  const MessagesScreen({super.key});

  @override
  State<MessagesScreen> createState() => _MessagesScreenState();
}

class _MessagesScreenState extends State<MessagesScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        backgroundColor: AppTheme.getBackgroundColor(context),
        elevation: 0,
        title: Text(
          'Messages',
          style: TextStyle(
            color: AppTheme.getTextColor(context),
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Search Bar for Messages
            Container(
              decoration: BoxDecoration(
                color: AppTheme.getSurfaceColor(context),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value.toLowerCase();
                  });
                },
                decoration: InputDecoration(
                  hintText: 'Search messages...',
                  hintStyle: TextStyle(color: AppTheme.getSecondaryTextColor(context)),
                  prefixIcon: Icon(
                    Icons.search,
                    color: AppTheme.getPrimaryColor(context),
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                ),
                style: TextStyle(
                  color: AppTheme.getTextColor(context),
                  fontSize: 16,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Messages List
            Expanded(
              child: StreamBuilder<List<ChatRoom>>(
                stream: ChatService.getUserChats(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  final chats = snapshot.data ?? [];

                  if (chats.isEmpty) {
                    return _buildEmptyState();
                  }

                  // Filter chats based on search query
                  final filteredChats = _searchQuery.isEmpty
                      ? chats
                      : chats.where((chat) {
                          // You can implement search logic here
                          // For now, just return all chats
                          return true;
                        }).toList();

                  return ListView.builder(
                    itemCount: filteredChats.length,
                    itemBuilder: (context, index) {
                      return _buildChatTile(filteredChats[index]);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: AppTheme.getSecondaryColor(context),
            ),
            const SizedBox(height: 16),
            Text(
              'No Messages Yet',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.getTextColor(context),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start a conversation by making swap or buy offers on items!',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.getSecondaryTextColor(context),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatTile(ChatRoom chatRoom) {
    return FutureBuilder<ChatParticipant?>(
      future: _getOtherParticipant(chatRoom),
      builder: (context, snapshot) {
        final otherParticipant = snapshot.data;
        final currentUser = AuthService.currentUser;
        final unreadCount = currentUser != null
            ? chatRoom.unreadCount[currentUser.uid] ?? 0
            : 0;

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: AppTheme.getSurfaceColor(context),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.all(16),
            leading: otherParticipant == null
                ? Shimmer.fromColors(
                    baseColor: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                    highlightColor: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.1),
                    child: CircleAvatar(
                      radius: 28,
                      backgroundColor: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                    ),
                  )
                : CircleAvatar(
                    radius: 28,
                    backgroundColor: AppTheme.getPrimaryColor(context),
                    backgroundImage: otherParticipant.profileImageUrl != null
                        ? CachedNetworkImageProvider(otherParticipant.profileImageUrl!)
                        : null,
                    child: otherParticipant.profileImageUrl == null
                        ? Text(
                            otherParticipant.name.isNotEmpty
                                ? otherParticipant.name[0].toUpperCase()
                                : '?',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                          )
                        : null,
                  ),
            title: Row(
              children: [
                Expanded(
                  child: otherParticipant == null
                      ? Shimmer.fromColors(
                          baseColor: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                          highlightColor: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.1),
                          child: Container(
                            height: 16,
                            width: 100,
                            decoration: BoxDecoration(
                              color: AppTheme.getSecondaryTextColor(context).withValues(alpha: 0.3),
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        )
                      : Text(
                          otherParticipant.name,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: AppTheme.getTextColor(context),
                          ),
                        ),
                ),
                if (unreadCount > 0)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.getPrimaryColor(context),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      unreadCount.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(
                  chatRoom.lastMessage ?? 'No messages yet',
                  style: TextStyle(
                    color: AppTheme.getSecondaryTextColor(context),
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      chatRoom.requestType == 'swap' ? Icons.swap_horiz : Icons.shopping_cart,
                      size: 14,
                      color: AppTheme.getPrimaryColor(context),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      chatRoom.requestType == 'swap' ? 'Swap Request' : 'Buy Request',
                      style: TextStyle(
                        color: AppTheme.getPrimaryColor(context),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    if (chatRoom.lastMessageTime != null)
                      Text(
                        _formatTime(chatRoom.lastMessageTime!),
                        style: TextStyle(
                          color: AppTheme.getSecondaryTextColor(context),
                          fontSize: 12,
                        ),
                      ),
                  ],
                ),
              ],
            ),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => ChatScreen(chatRoom: chatRoom),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Future<ChatParticipant?> _getOtherParticipant(ChatRoom chatRoom) async {
    final currentUser = AuthService.currentUser;
    if (currentUser == null) return null;

    final otherParticipantId = chatRoom.participants
        .firstWhere((id) => id != currentUser.uid, orElse: () => '');

    if (otherParticipantId.isEmpty) return null;

    return await ChatService.getUserData(otherParticipantId);
  }

  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return 'Yesterday';
      } else if (difference.inDays < 7) {
        return '${difference.inDays}d ago';
      } else {
        return '${timestamp.day}/${timestamp.month}';
      }
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
