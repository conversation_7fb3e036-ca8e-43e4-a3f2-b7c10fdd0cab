import 'dart:async';
import 'package:firebase_database/firebase_database.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/chat_models.dart';
import 'auth_service.dart';

class ChatService {
  static final FirebaseDatabase _realtimeDb = FirebaseDatabase.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Creates a new chat room for a swap request
  static Future<String?> createChatForSwapRequest({
    required String swapRequestId,
    required String requesterId,
    required String ownerId,
    required String targetItemId,
    String? offeredItemId,
    required String requestType,
    String? initialMessage,
  }) async {
    try {
      final chatId = _generateChatId(requesterId, ownerId, targetItemId);
      final now = DateTime.now();

      // Create chat room data
      final chatRoom = ChatRoom(
        chatId: chatId,
        participants: [requesterId, ownerId],
        swapRequestId: swapRequestId,
        targetItemId: targetItemId,
        offeredItemId: offeredItemId,
        requestType: requestType,
        createdAt: now,
        updatedAt: now,
        unreadCount: {requesterId: 0, ownerId: 1}, // Owner has 1 unread initially
      );

      // Save chat room to Realtime Database
      await _realtimeDb.ref('chats/$chatId').set(chatRoom.toMap());

      // Send initial system message about the swap request
      await _sendSystemMessage(
        chatId: chatId,
        requesterId: requesterId,
        ownerId: ownerId,
        requestType: requestType,
        initialMessage: initialMessage,
      );

      return chatId;
    } catch (e) {
      print('Error creating chat for swap request: $e');
      return null;
    }
  }

  /// Sends a system message when a swap request is created
  static Future<void> _sendSystemMessage({
    required String chatId,
    required String requesterId,
    required String ownerId,
    required String requestType,
    String? initialMessage,
  }) async {
    try {
      // Get requester and item details from Firestore
      final requesterDoc = await _firestore.collection('users').doc(requesterId).get();
      final requesterData = requesterDoc.data();
      final requesterName = requesterData?['displayName'] ?? requesterData?['name'] ?? 'Someone';

      // Create system message
      final systemMessage = requestType == 'swap'
          ? '$requesterName wants to swap an item with you!'
          : '$requesterName wants to buy your item!';

      await sendMessage(
        chatId: chatId,
        senderId: 'system',
        message: systemMessage,
        type: MessageType.swapRequest,
        metadata: {
          'swapRequestType': requestType,
          'requesterId': requesterId,
          'ownerId': ownerId,
        },
      );

      // Send user's initial message if provided
      if (initialMessage != null && initialMessage.isNotEmpty) {
        await sendMessage(
          chatId: chatId,
          senderId: requesterId,
          message: initialMessage,
          type: MessageType.text,
        );
      }
    } catch (e) {
      print('Error sending system message: $e');
    }
  }

  /// Sends a message in a chat
  static Future<bool> sendMessage({
    required String chatId,
    required String senderId,
    required String message,
    MessageType type = MessageType.text,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final messageRef = _realtimeDb.ref('chats/$chatId/messages').push();
      final messageId = messageRef.key!;
      final now = DateTime.now();

      final chatMessage = ChatMessage(
        messageId: messageId,
        chatId: chatId,
        senderId: senderId,
        message: message,
        type: type,
        timestamp: now,
        metadata: metadata,
      );

      // Send the message
      await messageRef.set(chatMessage.toMap());

      // Update chat room's last message info
      await _updateChatLastMessage(chatId, senderId, message, now);

      return true;
    } catch (e) {
      print('Error sending message: $e');
      return false;
    }
  }

  /// Updates the last message information in the chat room
  static Future<void> _updateChatLastMessage(
    String chatId,
    String senderId,
    String message,
    DateTime timestamp,
  ) async {
    try {
      final chatRef = _realtimeDb.ref('chats/$chatId');

      // Get current chat data to update unread counts
      final snapshot = await chatRef.get();
      if (snapshot.exists) {
        final chatData = Map<String, dynamic>.from(snapshot.value as Map);
        final participants = List<String>.from(chatData['participants'] ?? []);
        final currentUnreadCount = Map<String, int>.from(chatData['unreadCount'] ?? {});

        // Update unread count for other participants
        for (String participantId in participants) {
          if (participantId != senderId) {
            currentUnreadCount[participantId] = (currentUnreadCount[participantId] ?? 0) + 1;
          }
        }

        // Update chat room
        await chatRef.update({
          'lastMessage': message,
          'lastMessageSenderId': senderId,
          'lastMessageTime': timestamp.millisecondsSinceEpoch,
          'updatedAt': timestamp.millisecondsSinceEpoch,
          'unreadCount': currentUnreadCount,
        });
      }
    } catch (e) {
      print('Error updating chat last message: $e');
    }
  }

  /// Gets all chats for the current user
  static Stream<List<ChatRoom>> getUserChats() {
    final currentUser = AuthService.currentUser;
    if (currentUser == null) {
      return Stream.value([]);
    }

    return _realtimeDb
        .ref('chats')
        .orderByChild('updatedAt')
        .onValue
        .map((event) {
      final chats = <ChatRoom>[];
      if (event.snapshot.exists) {
        final chatsData = Map<String, dynamic>.from(event.snapshot.value as Map);

        for (final entry in chatsData.entries) {
          final chatData = Map<String, dynamic>.from(entry.value);
          final participants = List<String>.from(chatData['participants'] ?? []);

          // Only include chats where current user is a participant
          if (participants.contains(currentUser.uid)) {
            chats.add(ChatRoom.fromMap(chatData, entry.key));
          }
        }
      }

      // Sort by last message time (most recent first)
      chats.sort((a, b) {
        if (a.lastMessageTime == null && b.lastMessageTime == null) return 0;
        if (a.lastMessageTime == null) return 1;
        if (b.lastMessageTime == null) return -1;
        return b.lastMessageTime!.compareTo(a.lastMessageTime!);
      });

      return chats;
    });
  }

  /// Gets messages for a specific chat
  static Stream<List<ChatMessage>> getChatMessages(String chatId) {
    return _realtimeDb
        .ref('chats/$chatId/messages')
        .orderByChild('timestamp')
        .onValue
        .map((event) {
      final messages = <ChatMessage>[];
      if (event.snapshot.exists) {
        final messagesData = Map<String, dynamic>.from(event.snapshot.value as Map);

        for (final entry in messagesData.entries) {
          final messageData = Map<String, dynamic>.from(entry.value);
          messages.add(ChatMessage.fromMap(messageData, entry.key));
        }
      }

      // Sort by timestamp (oldest first for chat display)
      messages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      return messages;
    });
  }

  /// Marks messages as read for the current user
  static Future<void> markMessagesAsRead(String chatId) async {
    try {
      final currentUser = AuthService.currentUser;
      if (currentUser == null) return;

      final chatRef = _realtimeDb.ref('chats/$chatId');

      // Reset unread count for current user
      await chatRef.child('unreadCount/${currentUser.uid}').set(0);
    } catch (e) {
      print('Error marking messages as read: $e');
    }
  }

  /// Gets user data from Firestore for chat participants
  static Future<ChatParticipant?> getUserData(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        final userData = userDoc.data()!;
        return ChatParticipant(
          userId: userId,
          name: userData['displayName'] ?? userData['name'] ?? 'Unknown User',
          email: userData['email'] ?? '',
          profileImageUrl: userData['photoURL'] ?? userData['profileImageUrl'],
        );
      }
      return null;
    } catch (e) {
      print('Error getting user data: $e');
      return null;
    }
  }

  /// Gets item data from Firestore
  static Future<Map<String, dynamic>?> getItemData(String itemId) async {
    try {
      final itemDoc = await _firestore.collection('items').doc(itemId).get();
      if (itemDoc.exists) {
        final data = itemDoc.data()!;
        data['docId'] = itemDoc.id;
        return data;
      }
      return null;
    } catch (e) {
      print('Error getting item data: $e');
      return null;
    }
  }

  /// Generates a unique chat ID based on participants and item
  static String _generateChatId(String user1, String user2, String itemId) {
    final participants = [user1, user2]..sort();
    return '${participants[0]}_${participants[1]}_$itemId';
  }

  /// Checks if a chat already exists for a swap request
  static Future<String?> findExistingChat({
    required String requesterId,
    required String ownerId,
    required String targetItemId,
  }) async {
    try {
      final chatId = _generateChatId(requesterId, ownerId, targetItemId);
      final snapshot = await _realtimeDb.ref('chats/$chatId').get();

      if (snapshot.exists) {
        return chatId;
      }
      return null;
    } catch (e) {
      print('Error finding existing chat: $e');
      return null;
    }
  }

  /// Gets the total unread count for the current user
  static Stream<int> getTotalUnreadCount() {
    final currentUser = AuthService.currentUser;
    if (currentUser == null) {
      return Stream.value(0);
    }

    return getUserChats().map((chats) {
      int totalUnread = 0;
      for (final chat in chats) {
        totalUnread += chat.unreadCount[currentUser.uid] ?? 0;
      }
      return totalUnread;
    });
  }
}
