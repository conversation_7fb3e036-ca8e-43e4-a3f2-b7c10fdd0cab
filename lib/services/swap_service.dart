import 'package:cloud_firestore/cloud_firestore.dart';
import 'auth_service.dart';

class SwapService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Checks if the current user has posted any items
  /// Returns true if user has posted items, false otherwise
  static Future<bool> hasUserPostedItems() async {
    try {
      final user = AuthService.currentUser;
      if (user == null) return false;

      final querySnapshot = await _firestore
          .collection('items')
          .where('userId', isEqualTo: user.uid)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      print('Error checking user items: $e');
      return false;
    }
  }

  /// Fetches all active items posted by the current user
  /// Returns a list of user's items
  static Future<List<Map<String, dynamic>>> getUserPostedItems() async {
    try {
      final user = AuthService.currentUser;
      if (user == null) return [];

      final querySnapshot = await _firestore
          .collection('items')
          .where('userId', isEqualTo: user.uid)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['docId'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error fetching user items: $e');
      return [];
    }
  }

  /// Creates a swap request in the database
  /// 
  /// [targetItemId] - The item being requested
  /// [targetOwnerId] - The owner of the target item
  /// [offeredItemId] - The item being offered (null for simple buying)
  /// [requestType] - 'swap' or 'buy'
  /// [message] - Optional message from requester
  static Future<bool> createSwapRequest({
    required String targetItemId,
    required String targetOwnerId,
    String? offeredItemId,
    required String requestType,
    String? message,
  }) async {
    try {
      final user = AuthService.currentUser;
      if (user == null) return false;

      // Don't allow users to request their own items
      if (user.uid == targetOwnerId) {
        throw Exception('Cannot request your own item');
      }

      // Check if a similar request already exists
      final existingRequest = await _firestore
          .collection('swapRequests')
          .where('requesterId', isEqualTo: user.uid)
          .where('targetItemId', isEqualTo: targetItemId)
          .where('status', isEqualTo: 'pending')
          .get();

      if (existingRequest.docs.isNotEmpty) {
        throw Exception('You already have a pending request for this item');
      }

      // Create the swap request
      final requestData = {
        'requesterId': user.uid,
        'requesterEmail': user.email,
        'ownerId': targetOwnerId,
        'targetItemId': targetItemId,
        'offeredItemId': offeredItemId,
        'requestType': requestType,
        'status': 'pending',
        'message': message,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _firestore.collection('swapRequests').add(requestData);
      return true;
    } catch (e) {
      print('Error creating swap request: $e');
      rethrow;
    }
  }

  /// Gets swap requests for a specific item owner
  static Future<List<Map<String, dynamic>>> getSwapRequestsForOwner(String ownerId) async {
    try {
      final querySnapshot = await _firestore
          .collection('swapRequests')
          .where('ownerId', isEqualTo: ownerId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['docId'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error fetching swap requests: $e');
      return [];
    }
  }

  /// Gets swap requests made by a specific user
  static Future<List<Map<String, dynamic>>> getSwapRequestsByUser(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection('swapRequests')
          .where('requesterId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['docId'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error fetching user swap requests: $e');
      return [];
    }
  }

  /// Updates the status of a swap request
  static Future<bool> updateSwapRequestStatus({
    required String requestId,
    required String status,
  }) async {
    try {
      await _firestore.collection('swapRequests').doc(requestId).update({
        'status': status,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      return true;
    } catch (e) {
      print('Error updating swap request status: $e');
      return false;
    }
  }
}
