import 'package:flutter/material.dart';
import '../themes/app_theme.dart';
import '../services/swap_service.dart';

class SwapModals {
  /// Shows modal for users who haven't posted any items yet
  static Future<void> showNoPreviousItemsModal({
    required BuildContext context,
    required String targetItemId,
    required String targetOwnerId,
    required VoidCallback onSuccess,
  }) async {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.4,
          decoration: BoxDecoration(
            color: AppTheme.getBackgroundColor(context),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle bar
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: AppTheme.getSecondaryTextColor(context),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Title
                Text(
                  'No Items to Swap',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.getTextColor(context),
                  ),
                ),
                const SizedBox(height: 16),

                // Description
                Text(
                  'We see that you haven\'t posted anything yet to swap. Do you wish to continue with simple buying?',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppTheme.getSecondaryTextColor(context),
                    height: 1.5,
                  ),
                ),
                const Spacer(),

                // Buttons
                Row(
                  children: [
                    // Cancel button
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          side: BorderSide(
                            color: AppTheme.getSecondaryTextColor(context),
                          ),
                        ),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            color: AppTheme.getSecondaryTextColor(context),
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),

                    // Continue with buying button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () async {
                          Navigator.of(context).pop();
                          await _handleBuyRequest(
                            context: context,
                            targetItemId: targetItemId,
                            targetOwnerId: targetOwnerId,
                            onSuccess: onSuccess,
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.getPrimaryColor(context),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'Continue',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Shows modal for users to select which item they want to swap
  static Future<void> showItemSelectionModal({
    required BuildContext context,
    required List<Map<String, dynamic>> userItems,
    required String targetItemId,
    required String targetOwnerId,
    required VoidCallback onSuccess,
  }) async {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: BoxDecoration(
            color: AppTheme.getBackgroundColor(context),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
          ),
          child: Column(
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Handle bar
                    Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: AppTheme.getSecondaryTextColor(context),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Title
                    Text(
                      'Select Item to Swap',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.getTextColor(context),
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Subtitle
                    Text(
                      'Choose which of your items you\'d like to offer for this swap',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppTheme.getSecondaryTextColor(context),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              // Items list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  itemCount: userItems.length,
                  itemBuilder: (context, index) {
                    final item = userItems[index];
                    return _buildSelectableItemCard(
                      context: context,
                      item: item,
                      onTap: () async {
                        Navigator.of(context).pop();
                        await _handleSwapRequest(
                          context: context,
                          targetItemId: targetItemId,
                          targetOwnerId: targetOwnerId,
                          offeredItemId: item['itemId'],
                          onSuccess: onSuccess,
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Builds a selectable item card for the modal
  static Widget _buildSelectableItemCard({
    required BuildContext context,
    required Map<String, dynamic> item,
    required VoidCallback onTap,
  }) {
    final images = List<String>.from(item['images'] ?? []);
    final imageUrl = images.isNotEmpty ? images[0] : null;
    final price = item['price'];

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Item image
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: AppTheme.getBackgroundColor(context),
                  ),
                  child: imageUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.network(
                            imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Icon(
                                Icons.image_not_supported,
                                color: AppTheme.getSecondaryTextColor(context),
                                size: 32,
                              );
                            },
                          ),
                        )
                      : Icon(
                          Icons.image_not_supported,
                          color: AppTheme.getSecondaryTextColor(context),
                          size: 32,
                        ),
                ),
                const SizedBox(width: 16),

                // Item details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item['itemName'] ?? 'Unknown Item',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.getTextColor(context),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      if (price != null)
                        Text(
                          '₹${price.toStringAsFixed(0)}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.getPrimaryColor(context),
                          ),
                        )
                      else
                        Text(
                          'Free',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.getPrimaryColor(context),
                          ),
                        ),
                      const SizedBox(height: 4),
                      Text(
                        item['category'] ?? 'Unknown Category',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.getSecondaryTextColor(context),
                        ),
                      ),
                    ],
                  ),
                ),

                // Arrow icon
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppTheme.getSecondaryTextColor(context),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Handles buy request (no item to swap)
  static Future<void> _handleBuyRequest({
    required BuildContext context,
    required String targetItemId,
    required String targetOwnerId,
    required VoidCallback onSuccess,
  }) async {
    try {
      final success = await SwapService.createSwapRequest(
        targetItemId: targetItemId,
        targetOwnerId: targetOwnerId,
        requestType: 'buy',
      );

      // Check if context is still valid before using it
      if (context.mounted) {
        if (success) {
          onSuccess();
          _showSuccessSnackBar(context, 'Buy request sent successfully!');
        } else {
          _showErrorSnackBar(context, 'Failed to send buy request');
        }
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorSnackBar(context, e.toString());
      }
    }
  }

  /// Handles swap request with offered item
  static Future<void> _handleSwapRequest({
    required BuildContext context,
    required String targetItemId,
    required String targetOwnerId,
    required String offeredItemId,
    required VoidCallback onSuccess,
  }) async {
    try {
      final success = await SwapService.createSwapRequest(
        targetItemId: targetItemId,
        targetOwnerId: targetOwnerId,
        offeredItemId: offeredItemId,
        requestType: 'swap',
      );

      // Check if context is still valid before using it
      if (context.mounted) {
        if (success) {
          onSuccess();
          _showSuccessSnackBar(context, 'Swap offer sent successfully!');
        } else {
          _showErrorSnackBar(context, 'Failed to send swap offer');
        }
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorSnackBar(context, e.toString());
      }
    }
  }

  /// Shows success snackbar
  static void _showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.getPrimaryColor(context),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  /// Shows error snackbar
  static void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.getErrorColor(context),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}
