import 'package:flutter/material.dart';
import '../themes/app_theme.dart';
import '../services/swap_service.dart';

class SwapModals {
  /// Shows modal for users who haven't posted any items yet
  static Future<void> showNoPreviousItemsModal({
    required BuildContext context,
    required String targetItemId,
    required String targetOwnerId,
    required VoidCallback onSuccess,
  }) async {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.4,
          decoration: BoxDecoration(
            color: AppTheme.getBackgroundColor(context),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle bar
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: AppTheme.getSecondaryTextColor(context),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Title
                Text(
                  'No Items to Swap',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.getTextColor(context),
                  ),
                ),
                const SizedBox(height: 16),

                // Description
                Text(
                  'We see that you haven\'t posted anything yet to swap. Do you wish to continue with simple buying?',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppTheme.getSecondaryTextColor(context),
                    height: 1.5,
                  ),
                ),
                const Spacer(),

                // Buttons
                Row(
                  children: [
                    // Cancel button
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          side: BorderSide(
                            color: AppTheme.getSecondaryTextColor(context),
                          ),
                        ),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            color: AppTheme.getSecondaryTextColor(context),
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),

                    // Continue with buying button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () async {
                          Navigator.of(context).pop();
                          await _handleBuyRequest(
                            context: context,
                            targetItemId: targetItemId,
                            targetOwnerId: targetOwnerId,
                            onSuccess: onSuccess,
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.getPrimaryColor(context),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'Continue',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Shows modal for users to select which item they want to swap
  static Future<void> showItemSelectionModal({
    required BuildContext context,
    required List<Map<String, dynamic>> userItems,
    required String targetItemId,
    required String targetOwnerId,
    required VoidCallback onSuccess,
  }) async {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      builder: (BuildContext context) {
        return _ItemSelectionModalContent(
          userItems: userItems,
          targetItemId: targetItemId,
          targetOwnerId: targetOwnerId,
          onSuccess: onSuccess,
        );
      },
    );
  }



  /// Handles buy request (no item to swap)
  static Future<void> _handleBuyRequest({
    required BuildContext context,
    required String targetItemId,
    required String targetOwnerId,
    required VoidCallback onSuccess,
  }) async {
    try {
      final success = await SwapService.createSwapRequest(
        targetItemId: targetItemId,
        targetOwnerId: targetOwnerId,
        requestType: 'buy',
      );

      // Check if context is still valid before using it
      if (context.mounted) {
        if (success) {
          onSuccess();
          _showSuccessSnackBar(context, 'Buy request sent successfully!');
        } else {
          _showErrorSnackBar(context, 'Failed to send buy request');
        }
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorSnackBar(context, e.toString());
      }
    }
  }

  /// Handles swap request with offered item
  static Future<void> _handleSwapRequest({
    required BuildContext context,
    required String targetItemId,
    required String targetOwnerId,
    required String offeredItemId,
    required VoidCallback onSuccess,
  }) async {
    try {
      final success = await SwapService.createSwapRequest(
        targetItemId: targetItemId,
        targetOwnerId: targetOwnerId,
        offeredItemId: offeredItemId,
        requestType: 'swap',
      );

      // Check if context is still valid before using it
      if (context.mounted) {
        if (success) {
          onSuccess();
          _showSuccessSnackBar(context, 'Swap offer sent successfully!');
        } else {
          _showErrorSnackBar(context, 'Failed to send swap offer');
        }
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorSnackBar(context, e.toString());
      }
    }
  }

  /// Shows success snackbar
  static void _showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.getPrimaryColor(context),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  /// Shows error snackbar
  static void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.getErrorColor(context),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}

/// Stateful widget for the item selection modal with message input
class _ItemSelectionModalContent extends StatefulWidget {
  final List<Map<String, dynamic>> userItems;
  final String targetItemId;
  final String targetOwnerId;
  final VoidCallback onSuccess;

  const _ItemSelectionModalContent({
    required this.userItems,
    required this.targetItemId,
    required this.targetOwnerId,
    required this.onSuccess,
  });

  @override
  State<_ItemSelectionModalContent> createState() => _ItemSelectionModalContentState();
}

class _ItemSelectionModalContentState extends State<_ItemSelectionModalContent>
    with TickerProviderStateMixin {
  Map<String, dynamic>? _selectedItem;
  final TextEditingController _messageController = TextEditingController();
  bool _isLoading = false;
  bool _showCongratulations = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _messageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: AppTheme.getBackgroundColor(context),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: _showCongratulations ? _buildCongratulationsView() : _buildSelectionView(),
    );
  }

  Widget _buildSelectionView() {
    return Column(
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppTheme.getSecondaryTextColor(context),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 24),

              // Title
              Text(
                _selectedItem == null ? 'Select Item to Swap' : 'Add a Message',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.getTextColor(context),
                ),
              ),
              const SizedBox(height: 8),

              // Subtitle
              Text(
                _selectedItem == null
                    ? 'Choose which of your items you\'d like to offer for this swap'
                    : 'Write a message to the owner about your swap offer (optional)',
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.getSecondaryTextColor(context),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),

        // Content
        Expanded(
          child: _selectedItem == null ? _buildItemsList() : _buildMessageInput(),
        ),
      ],
    );
  }

  Widget _buildItemsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      itemCount: widget.userItems.length,
      itemBuilder: (context, index) {
        final item = widget.userItems[index];
        return _buildSelectableItemCard(
          item: item,
          onTap: () {
            setState(() {
              _selectedItem = item;
            });
          },
        );
      },
    );
  }

  Widget _buildMessageInput() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Selected item display
          _buildSelectedItemCard(),
          const SizedBox(height: 24),

          // Message input
          TextField(
            controller: _messageController,
            maxLines: 4,
            decoration: InputDecoration(
              hintText: 'Write a message about your swap offer...',
              hintStyle: TextStyle(
                color: AppTheme.getSecondaryTextColor(context),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: AppTheme.getSecondaryTextColor(context),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: AppTheme.getPrimaryColor(context),
                ),
              ),
            ),
            style: TextStyle(
              color: AppTheme.getTextColor(context),
            ),
          ),
          const Spacer(),

          // Buttons
          Row(
            children: [
              // Back button
              Expanded(
                child: OutlinedButton(
                  onPressed: _isLoading ? null : () {
                    setState(() {
                      _selectedItem = null;
                      _messageController.clear();
                    });
                  },
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    side: BorderSide(
                      color: AppTheme.getSecondaryTextColor(context),
                    ),
                  ),
                  child: Text(
                    'Back',
                    style: TextStyle(
                      color: AppTheme.getSecondaryTextColor(context),
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // Send offer button
              Expanded(
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _sendSwapOffer,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.getPrimaryColor(context),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isLoading
                      ? SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          'Send Offer',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedItemCard() {
    if (_selectedItem == null) return const SizedBox.shrink();

    final images = List<String>.from(_selectedItem!['images'] ?? []);
    final imageUrl = images.isNotEmpty ? images[0] : null;
    final price = _selectedItem!['price'];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.getPrimaryColor(context),
          width: 2,
        ),
      ),
      child: Row(
        children: [
          // Item image
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: AppTheme.getBackgroundColor(context),
            ),
            child: imageUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      imageUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.image_not_supported,
                          color: AppTheme.getSecondaryTextColor(context),
                          size: 24,
                        );
                      },
                    ),
                  )
                : Icon(
                    Icons.image_not_supported,
                    color: AppTheme.getSecondaryTextColor(context),
                    size: 24,
                  ),
          ),
          const SizedBox(width: 16),

          // Item details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _selectedItem!['itemName'] ?? 'Unknown Item',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.getTextColor(context),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                if (price != null)
                  Text(
                    '₹${price.toStringAsFixed(0)}',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.getPrimaryColor(context),
                    ),
                  )
                else
                  Text(
                    'Free',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.getPrimaryColor(context),
                    ),
                  ),
              ],
            ),
          ),

          // Selected indicator
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.getPrimaryColor(context),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.check,
              color: Colors.white,
              size: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectableItemCard({
    required Map<String, dynamic> item,
    required VoidCallback onTap,
  }) {
    final images = List<String>.from(item['images'] ?? []);
    final imageUrl = images.isNotEmpty ? images[0] : null;
    final price = item['price'];

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Item image
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: AppTheme.getBackgroundColor(context),
                  ),
                  child: imageUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.network(
                            imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Icon(
                                Icons.image_not_supported,
                                color: AppTheme.getSecondaryTextColor(context),
                                size: 32,
                              );
                            },
                          ),
                        )
                      : Icon(
                          Icons.image_not_supported,
                          color: AppTheme.getSecondaryTextColor(context),
                          size: 32,
                        ),
                ),
                const SizedBox(width: 16),

                // Item details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item['itemName'] ?? 'Unknown Item',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.getTextColor(context),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      if (price != null)
                        Text(
                          '₹${price.toStringAsFixed(0)}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.getPrimaryColor(context),
                          ),
                        )
                      else
                        Text(
                          'Free',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.getPrimaryColor(context),
                          ),
                        ),
                      const SizedBox(height: 4),
                      Text(
                        item['category'] ?? 'Unknown Category',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.getSecondaryTextColor(context),
                        ),
                      ),
                    ],
                  ),
                ),

                // Arrow icon
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppTheme.getSecondaryTextColor(context),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCongratulationsView() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Animated checkmark
          ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: AppTheme.getPrimaryColor(context),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check,
                color: Colors.white,
                size: 60,
              ),
            ),
          ),
          const SizedBox(height: 32),

          // Congratulations text
          Text(
            'Congratulations!',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppTheme.getTextColor(context),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),

          // Success message
          Text(
            'Your swap offer has been successfully sent to the owner. We\'ll notify you once they respond!',
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.getSecondaryTextColor(context),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 48),

          // Done button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                widget.onSuccess();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.getPrimaryColor(context),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Done',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _sendSwapOffer() async {
    if (_selectedItem == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await SwapService.createSwapRequest(
        targetItemId: widget.targetItemId,
        targetOwnerId: widget.targetOwnerId,
        offeredItemId: _selectedItem!['itemId'],
        requestType: 'swap',
        message: _messageController.text.trim().isEmpty ? null : _messageController.text.trim(),
      );

      if (success && mounted) {
        setState(() {
          _isLoading = false;
          _showCongratulations = true;
        });
        _animationController.forward();
      } else if (mounted) {
        setState(() {
          _isLoading = false;
        });
        SwapModals._showErrorSnackBar(context, 'Failed to send swap offer');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        SwapModals._showErrorSnackBar(context, e.toString());
      }
    }
  }
}
